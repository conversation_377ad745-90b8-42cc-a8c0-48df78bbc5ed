import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import 'api/api_service.dart';
import '../config/api_config.dart';

/// خدمة رفع الملفات المحسنة - متوافقة مع نظام التخزين المحلي المتقدم
/// توفر وظائف متقدمة لرفع الملفات مع دعم الضغط والنسخ الاحتياطي
class UploadService {
  // Singleton instance
  static final UploadService _instance = UploadService._internal();
  factory UploadService() => _instance;
  UploadService._internal();

  final ApiService _apiService = ApiService();
  
  // إعدادات الرفع
  static const int maxFileSize = 200 * 1024 * 1024; // 200MB
  static const List<String> allowedImageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  static const List<String> allowedDocumentTypes = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'];
  static const List<String> allowedVideoTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'];
  static const List<String> allowedAudioTypes = ['.mp3', '.wav', '.flac', '.aac', '.ogg'];
  static const List<String> allowedDataTypes = ['.csv', '.json', '.xml', '.xlsx', '.xls'];
  static const List<String> allowedArchiveTypes = ['.zip', '.rar', '.7z'];

  /// رفع ملف إلى الخادم
  ///
  /// [file] الملف المراد رفعه
  /// [fileName] اسم الملف
  /// [onProgress] دالة يتم استدعاؤها لتحديث نسبة التقدم
  ///
  /// يعيد URL الملف بعد الرفع
  Future<String?> uploadFile(File file, String fileName, {Function(double)? onProgress}) async {
    try {
      debugPrint('🔄 بدء رفع الملف: $fileName');

      // إنشاء طلب multipart
      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Upload/file');
      final request = http.MultipartRequest('POST', uri);

      // إضافة رؤوس HTTP
      final token = _apiService.accessToken;
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // إضافة الملف
      final multipartFile = await http.MultipartFile.fromPath(
        'file',
        file.path,
        filename: fileName,
      );
      request.files.add(multipartFile);

      // إرسال الطلب مع تتبع التقدم
      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        final response = await http.Response.fromStream(streamedResponse);
        final responseData = _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );

        debugPrint('✅ تم رفع الملف بنجاح: ${responseData['url']}');
        return responseData['url'] as String?;
      } else {
        debugPrint('❌ فشل في رفع الملف: ${streamedResponse.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفع الملف: $e');
      return null;
    }
  }

  /// رفع صورة
  ///
  /// [imageFile] ملف الصورة
  /// [folder] مجلد الحفظ (اختياري)
  ///
  /// يعيد URL الصورة بعد الرفع
  Future<String?> uploadImage(File imageFile, {String? folder}) async {
    try {
      debugPrint('🔄 بدء رفع الصورة');

      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Upload/image');
      final request = http.MultipartRequest('POST', uri);

      // إضافة رؤوس HTTP
      final token = _apiService.accessToken;
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // إضافة الصورة
      final multipartFile = await http.MultipartFile.fromPath(
        'image',
        imageFile.path,
        filename: 'image_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      request.files.add(multipartFile);

      // إضافة المجلد إذا تم تحديده
      if (folder != null) {
        request.fields['folder'] = folder;
      }

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        final response = await http.Response.fromStream(streamedResponse);
        final responseData = _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );

        debugPrint('✅ تم رفع الصورة بنجاح: ${responseData['url']}');
        return responseData['url'] as String?;
      } else {
        debugPrint('❌ فشل في رفع الصورة: ${streamedResponse.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفع الصورة: $e');
      return null;
    }
  }

  /// رفع مرفق لمهمة مع التحسينات المتقدمة
  ///
  /// [file] الملف المراد رفعه
  /// [taskId] معرف المهمة
  /// [uploadedBy] معرف المستخدم
  /// [folder] مجلد التخزين المخصص (اختياري)
  /// [onProgress] دالة تتبع التقدم (اختياري)
  ///
  /// يعيد معلومات المرفق المحسنة بعد الرفع
  Future<Map<String, dynamic>?> uploadAttachmentEnhanced(
    File file, 
    int taskId, 
    int uploadedBy, {
    String? folder,
    Function(double)? onProgress,
  }) async {
    try {
      debugPrint('🔄 بدء رفع مرفق محسن للمهمة: $taskId');

      // التحقق من صحة الملف قبل الرفع
      final validation = validateFile(file);
      if (!validation['isValid']) {
        debugPrint('❌ فشل التحقق من الملف: ${validation['error']}');
        throw Exception(validation['error']);
      }

      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Attachments/upload');
      final request = http.MultipartRequest('POST', uri);

      // إضافة رؤوس HTTP
      final token = _apiService.accessToken;
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // إضافة الملف
      final multipartFile = await http.MultipartFile.fromPath(
        'file',
        file.path,
        filename: path.basename(file.path),
      );
      request.files.add(multipartFile);

      // إضافة البيانات الإضافية
      request.fields['taskId'] = taskId.toString();
      request.fields['uploadedBy'] = uploadedBy.toString();
      if (folder != null) {
        request.fields['folder'] = folder;
      }

      // إرسال الطلب مع تتبع التقدم
      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 201) {
        final response = await http.Response.fromStream(streamedResponse);
        final responseData = _apiService.handleResponse<Map<String, dynamic>>(
          response,
          (json) => json,
        );

        debugPrint('✅ تم رفع المرفق المحسن بنجاح: ${responseData['fileName']}');
        debugPrint('📊 معلومات الضغط: ${responseData['isCompressed'] ? 'مضغوط' : 'غير مضغوط'}');
        
        if (responseData['compressionRatio'] != null) {
          final ratio = (responseData['compressionRatio'] as double) * 100;
          debugPrint('📉 نسبة الضغط: ${ratio.toStringAsFixed(1)}%');
        }

        return responseData;
      } else {
        final errorResponse = await http.Response.fromStream(streamedResponse);
        final errorData = jsonDecode(errorResponse.body);
        debugPrint('❌ فشل في رفع المرفق: ${errorData['message'] ?? 'خطأ غير معروف'}');
        throw Exception(errorData['message'] ?? 'فشل في رفع الملف');
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفع المرفق المحسن: $e');
      rethrow;
    }
  }

  /// رفع مرفق لمهمة (الطريقة القديمة للتوافق مع الكود الموجود)
  Future<int?> uploadAttachment(File file, int taskId, {String? description}) async {
    try {
      // استخدام الطريقة المحسنة مع معرف مستخدم افتراضي
      final result = await uploadAttachmentEnhanced(file, taskId, 1); // معرف مستخدم افتراضي
      return result?['id'] as int?;
    } catch (e) {
      debugPrint('❌ خطأ في رفع المرفق: $e');
      return null;
    }
  }

  /// معاينة الملف قبل الرفع
  ///
  /// [file] الملف المراد معاينته
  ///
  /// يعيد معلومات المعاينة
  Future<Map<String, dynamic>?> previewFile(File file) async {
    try {
      final String fileName = path.basename(file.path);
      final String fileExtension = path.extension(file.path).toLowerCase();
      final int fileSize = await file.length();
      final String fileType = lookupMimeType(file.path) ?? 'application/octet-stream';

      return {
        'fileName': fileName,
        'fileExtension': fileExtension,
        'fileSize': fileSize,
        'fileType': fileType,
        'filePath': file.path,
        'isImage': _isImageFile(fileExtension),
        'isDocument': _isDocumentFile(fileExtension),
        'formattedSize': _formatFileSize(fileSize),
      };
    } catch (e) {
      debugPrint('خطأ في معاينة الملف: $e');
      return null;
    }
  }

  /// التحقق من كون الملف صورة
  bool _isImageFile(String extension) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.contains(extension.toLowerCase());
  }

  /// التحقق من كون الملف مستند
  bool _isDocumentFile(String extension) {
    const documentExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf'];
    return documentExtensions.contains(extension.toLowerCase());
  }

  /// التحقق من صحة الملف قبل الرفع
  ///
  /// [file] الملف المراد التحقق منه
  ///
  /// يعيد خريطة تحتوي على نتيجة التحقق ورسالة الخطأ إن وجدت
  Map<String, dynamic> validateFile(File file) {
    try {
      // التحقق من وجود الملف
      if (!file.existsSync()) {
        return {'isValid': false, 'error': 'الملف غير موجود'};
      }

      // التحقق من حجم الملف
      final fileSize = file.lengthSync();
      if (fileSize == 0) {
        return {'isValid': false, 'error': 'الملف فارغ'};
      }

      if (fileSize > maxFileSize) {
        final maxSizeMB = (maxFileSize / (1024 * 1024)).toStringAsFixed(0);
        return {'isValid': false, 'error': 'حجم الملف كبير جداً. الحد الأقصى $maxSizeMB MB'};
      }

      // التحقق من نوع الملف
      final extension = path.extension(file.path).toLowerCase();
      final allAllowedTypes = [
        ...allowedImageTypes,
        ...allowedDocumentTypes,
        ...allowedVideoTypes,
        ...allowedAudioTypes,
        ...allowedDataTypes,
        ...allowedArchiveTypes,
      ];

      if (!allAllowedTypes.contains(extension)) {
        return {'isValid': false, 'error': 'نوع الملف غير مدعوم: $extension'};
      }

      return {'isValid': true, 'error': null};
    } catch (e) {
      return {'isValid': false, 'error': 'خطأ في التحقق من الملف: $e'};
    }
  }

  /// الحصول على إحصائيات المرفقات من الخادم
  ///
  /// يعيد إحصائيات شاملة عن المرفقات المرفوعة
  Future<Map<String, dynamic>?> getAttachmentsStatistics() async {
    try {
      debugPrint('🔄 جلب إحصائيات المرفقات');

      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Attachments/statistics');
      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer ${_apiService.accessToken}',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        debugPrint('✅ تم جلب الإحصائيات بنجاح');
        return data;
      } else {
        debugPrint('❌ فشل في جلب الإحصائيات: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
      return null;
    }
  }

  /// تنظيف الملفات المحذوفة القديمة
  ///
  /// [olderThanDays] حذف الملفات المحذوفة منذ أكثر من عدد الأيام المحدد
  ///
  /// يعيد عدد الملفات المحذوفة
  Future<Map<String, dynamic>?> cleanupOldFiles({int olderThanDays = 30}) async {
    try {
      debugPrint('🔄 بدء تنظيف الملفات القديمة (أكثر من $olderThanDays يوم)');

      final uri = Uri.parse('${ApiConfig.baseUrl}/api/Attachments/cleanup?olderThanDays=$olderThanDays');
      final response = await http.post(
        uri,
        headers: {
          'Authorization': 'Bearer ${_apiService.accessToken}',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        debugPrint('✅ تم التنظيف بنجاح: ${data['message']}');
        return data;
      } else {
        debugPrint('❌ فشل في التنظيف: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في التنظيف: $e');
      return null;
    }
  }

  /// تحديد مجلد التخزين المناسب للملف
  ///
  /// [file] الملف المراد تحديد مجلده
  ///
  /// يعيد اسم المجلد المناسب
  String determineStorageFolder(File file) {
    final extension = path.extension(file.path).toLowerCase();
    
    if (allowedImageTypes.contains(extension)) {
      return 'images';
    } else if (allowedDocumentTypes.contains(extension)) {
      return 'documents';
    } else if (allowedVideoTypes.contains(extension)) {
      return 'videos';
    } else if (allowedAudioTypes.contains(extension)) {
      return 'audio';
    } else if (allowedDataTypes.contains(extension)) {
      return 'data';
    } else if (allowedArchiveTypes.contains(extension)) {
      return 'archives';
    }

    return 'attachments';
  }

  /// التحقق من كون الملف قابل للضغط
  ///
  /// [file] الملف المراد التحقق منه
  ///
  /// يعيد true إذا كان الملف قابل للضغط
  bool isCompressible(File file) {
    final extension = path.extension(file.path).toLowerCase();
    // الصور عادة قابلة للضغط
    return ['.jpg', '.jpeg', '.png', '.bmp'].contains(extension);
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
