import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';

// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart'; // تم تعليقه مؤقتًا بسبب مشكلة التوافق
import 'package:flutter_application_2/controllers/task_controller.dart';
import 'package:flutter_application_2/controllers/attachments_controller.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';

import 'package:flutter_application_2/models/user_model.dart';
import 'package:flutter_application_2/utils/file_processor.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/screens/widgets/drop_zone_widget.dart';
import 'package:flutter_application_2/screens/widgets/file_viewer_widget.dart';
import 'package:flutter_application_2/screens/widgets/attachment_preview_widget.dart';
import 'package:flutter_application_2/services/download_service.dart';
import 'package:flutter_application_2/services/unified_signalr_service.dart';
import 'package:flutter_application_2/services/unified_permission_service.dart';

/// علامة تبويب المرفقات
/// تعرض المرفقات المرتبطة بالمهمة وتتيح إضافة مرفقات جديدة
class TaskAttachmentsTab extends StatefulWidget {
  final List<Attachment>? attachments;
  const TaskAttachmentsTab({super.key, this.attachments});

  @override
  State<TaskAttachmentsTab> createState() => _TaskAttachmentsTabState();
}

class _TaskAttachmentsTabState extends State<TaskAttachmentsTab> {
  final TaskController _taskController = Get.find<TaskController>();
  final AttachmentsController _attachmentsController = Get.find<AttachmentsController>();
  final AuthController _authController = Get.find<AuthController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  // فلتر المرفقات
  String _filterType = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();

    // 🔒 فحص الصلاحيات في بداية الشاشة - طبقة حماية أساسية
    if (!_permissionService.canViewAttachments()) {
      // إظهار رسالة خطأ والعودة للصفحة السابقة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.snackbar(
          'غير مسموح',
          'ليس لديك صلاحية لعرض المرفقات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.statusCancelled.withAlpha(26),
          colorText: AppColors.statusCancelled,
          duration: const Duration(seconds: 3),
        );
        Get.back(); // العودة للصفحة السابقة
      });
      return;
    }

    final signalRService = Get.find<UnifiedSignalRService>();

    // الانضمام لمجموعة المهمة للحصول على إشعارات المرفقات
    if (_taskController.currentTask != null) {
      Future.microtask(() => signalRService.joinTaskGroupForAttachments(_taskController.currentTask!.id.toString()));
    }

    // الاشتراك في إشعار إضافة مرفق - تحديث سريع للمرفقات فقط
    signalRService.taskHubConnection?.on("AttachmentAdded", (arguments) async {
      try {
        if (arguments == null || arguments.isEmpty) return;

        // التحقق من صحة المعاملات وتجنب null values
        final taskIdArg = arguments.isNotEmpty ? arguments[0] : null;
        if (taskIdArg == null) return;

        final int? taskId = taskIdArg is int ? taskIdArg : int.tryParse(taskIdArg.toString());
        if (taskId != null && _taskController.currentTask?.id == taskId) {
          debugPrint('🔔 استقبال إشعار SignalR - إضافة مرفق للمهمة $taskId');

          // تحديث سريع للمرفقات فقط بدلاً من تحديث المهمة كاملة
          await _taskController.refreshTaskAttachments(taskId);

          // تحديث فوري للواجهة مع تحديث إضافي
          if (mounted) {
            setState(() {});
            // تحديث إضافي بعد تأخير قصير لضمان التحديث
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                setState(() {});
              }
            });
          }

          debugPrint('✅ تم تحديث المرفقات بعد إشعار SignalR - إضافة مرفق');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة إشعار إضافة مرفق: $e');
        // تجاهل الخطأ لتجنب crash التطبيق
      }
    });

    // الاشتراك في إشعار حذف مرفق - تحديث سريع للمرفقات فقط
    signalRService.taskHubConnection?.on("AttachmentDeleted", (arguments) async {
      try {
        if (arguments == null || arguments.isEmpty) return;

        // التحقق من صحة المعاملات وتجنب null values
        final taskIdArg = arguments.isNotEmpty ? arguments[0] : null;
        if (taskIdArg == null) return;

        final int? taskId = taskIdArg is int ? taskIdArg : int.tryParse(taskIdArg.toString());
        if (taskId != null && _taskController.currentTask?.id == taskId) {
          debugPrint('🔔 استقبال إشعار SignalR - حذف مرفق من المهمة $taskId');

          // تحديث سريع للمرفقات فقط بدلاً من تحديث المهمة كاملة
          await _taskController.refreshTaskAttachments(taskId);

          // تحديث فوري للواجهة مع تحديث إضافي
          if (mounted) {
            setState(() {});
            // تحديث إضافي بعد تأخير قصير لضمان التحديث
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                setState(() {});
              }
            });
          }

          debugPrint('✅ تم تحديث المرفقات بعد إشعار SignalR - حذف مرفق');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة إشعار حذف مرفق: $e');
        // تجاهل الخطأ لتجنب crash التطبيق
      }
    });
  }

  @override
  void dispose() {
    // مغادرة مجموعة المهمة عند إغلاق الشاشة
    if (_taskController.currentTask != null) {
      final signalRService = Get.find<UnifiedSignalRService>();
      Future.microtask(() => signalRService.leaveTaskGroupForAttachments(_taskController.currentTask!.id.toString()));
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام GetBuilder مع معرفات متعددة لضمان التحديث
    return GetBuilder<TaskController>(
      id: 'task_attachments', // استخدام معرف المرفقات المحدد
      builder: (controller) {
        // يجب الاعتماد فقط على controller.currentTask وعدم استخدام أي نسخة محلية
        final task = controller.currentTask;

        if (task == null) {
          return const Center(child: Text('لم يتم تحميل المهمة'));
        }

        // استخدم قائمة المرفقات من الكائن الحالي للمهمة دائماً
        // لا تحفظ أي نسخة محلية أو متغير خارجي
        final filteredAttachments = _getFilteredAttachments(List<Attachment>.from(task.attachments));
        debugPrint('📦 [task_attachments_tab] attachmentsCount: ${task.attachments.length}');

        return GetBuilder<TaskController>(
          id: 'task_details', // إضافة GetBuilder إضافي للتأكد من التحديث
          builder: (detailsController) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان القسم
                  Text(
                    'مرفقات المهمة',
                    style: AppStyles.titleLarge.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w800,
                      shadows: [
                        Shadow(
                          color: AppColors.getShadowColor(0.2),
                          blurRadius: 1,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // شريط البحث والفلترة
                  _buildSearchAndFilterBar(),
                  const SizedBox(height: 16),

                  // زر إضافة مرفق - محمي بالصلاحيات
                  if (_permissionService.canUploadAttachments())
                    _buildAddAttachmentButton(),
                  if (_permissionService.canUploadAttachments())
                    const SizedBox(height: 16),

                  // منطقة سحب وإسقاط الملفات
                  Expanded(
                    child: DropZoneWidget(
                      onDroppedFiles: _handleDroppedFiles,
                      child: _buildAttachmentsList(filteredAttachments),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// معالجة الملفات التي تم إسقاطها
  Future<void> _handleDroppedFiles(List<XFile> files) async {
    // 🔒 التحقق من صلاحية رفع المرفقات
    if (!_permissionService.canUploadAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لرفع المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.statusCancelled.withAlpha(26),
        colorText: AppColors.statusCancelled,
      );
      return;
    }

    final task = _taskController.currentTask;
    if (task == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // معالجة كل ملف تم إسقاطه
      for (final file in files) {
        final filePath = file.path;
        final fileName = file.name;
        final fileSize = await file.length();
        final fileType = lookupMimeType(filePath) ?? 'application/octet-stream';

        // رفع الملف
        await _uploadAttachment(filePath, fileType, fileName, fileSize);
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'نجاح'.tr,
        'تم رفع ${files.length} ملف بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.statusCompleted.withAlpha(26),
        colorText: AppColors.statusCompleted,
      );

      // تحديث فوري وشامل للواجهة
      _taskController.update(['task_details', 'task_attachments', 'task_overview']);
      if (mounted) {
        setState(() {});
        // تحديث إضافي بعد تأخير قصير
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            setState(() {});
          }
        });
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء رفع الملفات: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// بناء شريط البحث والفلترة
  Widget _buildSearchAndFilterBar() {
    return Row(
      children: [
        // حقل البحث
        Expanded(
          child: TextField(
            style: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            decoration: InputDecoration(
              hintText: 'بحث في المرفقات'.tr,
              hintStyle: TextStyle(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              prefixIcon: Icon(Icons.search, color: AppColors.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primary, width: 2.0),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        const SizedBox(width: 8),

        // قائمة الفلترة
        DropdownButton<String>(
          value: _filterType,
          icon: const Icon(Icons.filter_list),
          underline: const SizedBox(),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _filterType = newValue;
              });
            }
          },
          items: [
            DropdownMenuItem<String>(
              value: 'all',
              child: Text('الكل'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'image',
              child: Text('صور'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'document',
              child: Text('مستندات'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'video',
              child: Text('فيديو'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'audio',
              child: Text('صوت'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'transfer',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.swap_horiz, size: 16, color: AppColors.primary),
                  const SizedBox(width: 4),
                  Text('مرفقات التحويل'.tr, style: TextStyle(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  )),
                ],
              ),
            ),
            DropdownMenuItem<String>(
              value: 'normal',
              child: Text('مرفقات عادية'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
            DropdownMenuItem<String>(
              value: 'other',
              child: Text('أخرى'.tr, style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              )),
            ),
          ],
        ),
      ],
    );
  }

  /// فلترة المرفقات (تأخذ القائمة كمُدخل)
  List<Attachment> _getFilteredAttachments(List<Attachment> attachments) {
    List<Attachment> filtered = List.from(attachments);
    debugPrint('📦 جميع المرفقات قبل الفلترة: ${attachments.map((a) => a.fileName).join(', ')}');

    // فلترة حسب البحث (تجاهل إذا كان البحث فارغ أو قصير جداً)
    if (_searchQuery.isNotEmpty && _searchQuery.length > 2) {
      filtered = filtered.where((attachment) {
        return attachment.fileName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
      debugPrint('🔍 بعد فلترة البحث "$_searchQuery": ${filtered.map((a) => a.fileName).join(', ')}');
    }

    // فلترة حسب النوع
    if (_filterType != 'all') {
      filtered = filtered.where((attachment) {
        // فلترة خاصة لمرفقات التحويل
        if (_filterType == 'transfer') {
          return attachment.isTransferAttachment;
        }

        // فلترة خاصة للمرفقات العادية
        if (_filterType == 'normal') {
          return !attachment.isTransferAttachment;
        }

        // فلترة حسب نوع الملف العادي
        final attachmentMimeCategory = _getMimeCategory(attachment.fileType, attachment.fileName);
        debugPrint('🔍 فلترة المرفق: ${attachment.fileName} - نوع MIME: ${attachment.fileType} - تصنيف: $attachmentMimeCategory - فلتر: $_filterType');
        return attachmentMimeCategory == _filterType;
      }).toList();
    }

    debugPrint('🟠 بعد الفلترة النهائية: ${filtered.map((a) => a.fileName).join(', ')}');
    return filtered;
  }

  /// بناء زر إضافة مرفق
  Widget _buildAddAttachmentButton() {
    return ElevatedButton.icon(
      onPressed: _showAttachmentDialog,
      icon: Icon(Icons.add, color: AppColors.white),
      label: Text('إضافة مرفق'.tr, style: TextStyle(
        color: AppColors.white,
        fontWeight: FontWeight.w600,
      )),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// بناء قائمة المرفقات
  Widget _buildAttachmentsList(List<Attachment> attachments) {
    
    if (attachments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.attach_file,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مرفقات'.tr,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w700,
                shadows: [
                  Shadow(
                    color: AppColors.getShadowColor(0.1),
                    blurRadius: 0.5,
                    offset: const Offset(0, 0.5),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اسحب الملفات هنا أو اضغط على زر إضافة مرفق'.tr,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                    color: AppColors.getShadowColor(0.08),
                    blurRadius: 0.3,
                    offset: const Offset(0, 0.3),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    final int crossAxisCount = ResponsiveHelper.isMobile(context)
        ? 2
        : ResponsiveHelper.isTablet(context)
            ? 3
            : 4;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: attachments.length,
      itemBuilder: (context, index) {
        final attachment = attachments[index];

        return AttachmentPreview(
          attachment: attachment,
          onView: _viewAttachment,
          onOpen: !kIsWeb ? _openAttachment : null, // فقط للتطبيقات المحلية وليس للويب
          onDownload: _downloadAttachment,
          onDelete: _canDeleteAttachment(attachment) ? _deleteAttachment : null,
          canDelete: _canDeleteAttachment(attachment),
        );
      },
    );
  }

  /// عرض مربع حوار إضافة مرفق
  void _showAttachmentDialog() {
    // 🔒 التحقق من صلاحية رفع المرفقات
    if (!_permissionService.canUploadAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لرفع المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    final task = _taskController.currentTask;
    if (task == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('إضافة مرفق'.tr, style: TextStyle(
          color: AppColors.primary,
          fontWeight: FontWeight.w800,
          shadows: [
            Shadow(
              color: AppColors.getShadowColor(0.2),
              blurRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        )),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // قسم الصور
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '📷 الصور والكاميرا',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              ListTile(
                leading: Icon(Icons.photo_library, color: Colors.green),
                title: Text('اختيار صورة من المعرض'.tr, style: TextStyle(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                )),
                subtitle: Text('JPG, PNG, GIF, BMP, WebP', style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                )),
                onTap: () {
                  Get.back();
                  _pickImageFromGallery();
                },
              ),
              ListTile(
                leading: Icon(Icons.camera_alt, color: Colors.blue),
                title: Text('التقاط صورة'.tr, style: TextStyle(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                )),
                subtitle: Text('التقاط صورة جديدة بالكاميرا', style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                )),
                onTap: () {
                  Get.back();
                  _pickImageFromCamera();
                },
              ),

              const Divider(),

              // قسم الملفات العامة
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.purple.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '📁 جميع أنواع الملفات',
                  style: TextStyle(
                    color: Colors.purple,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
              ListTile(
                leading: Icon(Icons.upload_file, color: Colors.purple),
                title: Text('اختيار أي ملف'.tr, style: TextStyle(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                )),
                subtitle: Text('جميع أنواع الملفات مدعومة', style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                )),
                onTap: () {
                  Get.back();
                  _pickFile();
                },
              ),

              // معلومات إضافية
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withAlpha(51)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'أنواع الملفات المدعومة:',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• المستندات: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT\n'
                      '• الصور: JPG, PNG, GIF, BMP, SVG, WebP, TIFF\n'
                      '• الفيديو: MP4, AVI, MOV, WMV, FLV, MKV, WebM\n'
                      '• الصوت: MP3, WAV, OGG, AAC, FLAC, M4A\n'
                      '• الأرشيف: ZIP, RAR, 7Z, TAR, GZ\n'
                      '• البرمجة: JS, CSS, HTML, PHP, PY, JAVA, SQL\n'
                      '• التنفيذية: EXE, APK, MSI, DEB, RPM, DMG\n'
                      '• وجميع الأنواع الأخرى',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 11,
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr, style: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            )),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    // 🔒 التحقق من صلاحية رفع المرفقات
    if (!_permissionService.canUploadAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لرفع المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // التأكد من أن اسم الملف غير فارغ
        final fileSize = await image.length();
        final imageName = image.name.isNotEmpty ? image.name : 'image_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
        _uploadAttachment(
          image.path, 
          image.mimeType ?? 'image/${imageName.split('.').last}', 
          imageName,
          fileSize,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        final fileSize = await image.length();
        final imageName = image.name.isNotEmpty ? image.name : 'image_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
        _uploadAttachment(
          image.path, 
          image.mimeType ?? 'image/${imageName.split('.').last}', 
          imageName,
          fileSize,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء التقاط الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// اختيار ملف مع دعم شامل لجميع أنواع الملفات
  Future<void> _pickFile() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      // دعم جميع أنواع الملفات بدون قيود
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any, // قبول جميع أنواع الملفات
        allowMultiple: false,
        withData: true, // تحميل بيانات الملف للمعالجة
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path != null) {
          // التحقق من نوع الملف والتحذير للملفات التنفيذية
          final fileExtension = file.extension?.toLowerCase() ?? '';
          final isExecutableFile = _isExecutableFile(fileExtension);

          if (isExecutableFile) {
            // عرض تحذير للملفات التنفيذية
            final confirmed = await _showExecutableFileWarning(file.name, fileExtension);
            if (!confirmed) return;
          }

          // تحديد نوع MIME بشكل شامل
          final mimeType = _determineMimeType(file.name, file.extension, file.bytes);

          // رفع الملف
          await _uploadAttachment(file.path!, mimeType, file.name, file.size);
        } else {
          // معالجة الملفات على الويب
          Get.snackbar(
            'تنبيه'.tr,
            'لا يمكن الوصول إلى مسار الملف. قد يكون هذا بسبب قيود المتصفح.'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.amber.shade100,
            colorText: Colors.amber.shade800,
          );
        }
      } else {
        // لم يتم اختيار ملف
        Get.snackbar(
          'معلومة'.tr,
          'لم يتم اختيار أي ملف.'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// التحقق من أن الملف تنفيذي
  bool _isExecutableFile(String extension) {
    final executableExtensions = [
      'exe', 'msi', 'bat', 'cmd', 'com', 'scr', 'pif', // Windows
      'apk', 'dex', // Android
      'app', 'dmg', 'pkg', // macOS
      'deb', 'rpm', 'snap', 'appimage', // Linux
      'sh', 'bash', 'zsh', 'fish', // Shell scripts
      'ps1', 'psm1', // PowerShell
      'jar', 'war', 'ear', // Java
      'run', 'bin', // Generic executables
    ];
    return executableExtensions.contains(extension);
  }

  /// عرض تحذير للملفات التنفيذية
  Future<bool> _showExecutableFileWarning(String fileName, String extension) async {
    return await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: AppColors.dialog,
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange, size: 28),
            const SizedBox(width: 8),
            Text('تحذير أمني', style: AppStyles.titleMedium),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أنت على وشك رفع ملف تنفيذي:',
              style: AppStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('اسم الملف: $fileName', style: AppStyles.bodySmall),
                  Text('النوع: .$extension', style: AppStyles.bodySmall),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '⚠️ تنبيه: الملفات التنفيذية قد تحتوي على برامج ضارة. تأكد من مصدر الملف قبل المتابعة.',
              style: AppStyles.bodySmall.copyWith(color: Colors.orange.shade700),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('إلغاء', style: TextStyle(color: AppColors.textSecondary)),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('متابعة بحذر'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// تحديد نوع MIME بشكل شامل
  String _determineMimeType(String fileName, String? extension, Uint8List? bytes) {
    // محاولة استخدام lookupMimeType أولاً
    String? mimeType = lookupMimeType(fileName, headerBytes: bytes?.take(1024).toList());

    if (mimeType != null) return mimeType;

    // إذا فشل، استخدم الامتداد لتحديد النوع
    final ext = (extension ?? fileName.split('.').last).toLowerCase();

    // أنواع MIME للملفات الشائعة
    final mimeTypes = <String, String>{
      // مستندات
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'txt': 'text/plain',
      'rtf': 'application/rtf',
      'csv': 'text/csv',

      // صور
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'svg': 'image/svg+xml',
      'webp': 'image/webp',
      'tiff': 'image/tiff',

      // فيديو
      'mp4': 'video/mp4',
      'avi': 'video/x-msvideo',
      'mov': 'video/quicktime',
      'wmv': 'video/x-ms-wmv',
      'flv': 'video/x-flv',
      'mkv': 'video/x-matroska',
      'webm': 'video/webm',

      // صوت
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',
      'aac': 'audio/aac',
      'm4a': 'audio/mp4',
      'flac': 'audio/flac',

      // أرشيف
      'zip': 'application/zip',
      'rar': 'application/vnd.rar',
      '7z': 'application/x-7z-compressed',
      'tar': 'application/x-tar',
      'gz': 'application/gzip',

      // ملفات تطوير
      'json': 'application/json',
      'xml': 'application/xml',
      'html': 'text/html',
      'css': 'text/css',
      'js': 'application/javascript',
      'php': 'application/x-httpd-php',
      'py': 'text/x-python',
      'java': 'text/x-java-source',
      'cpp': 'text/x-c++src',
      'c': 'text/x-csrc',
      'h': 'text/x-chdr',
      'sql': 'application/sql',

      // ملفات تنفيذية
      'exe': 'application/vnd.microsoft.portable-executable',
      'msi': 'application/x-msi',
      'apk': 'application/vnd.android.package-archive',
      'deb': 'application/vnd.debian.binary-package',
      'rpm': 'application/x-rpm',
      'dmg': 'application/x-apple-diskimage',
      'app': 'application/x-apple-app',
      'jar': 'application/java-archive',
      'war': 'application/java-archive',

      // ملفات نصوص برمجية
      'bat': 'application/x-bat',
      'cmd': 'application/x-bat',
      'sh': 'application/x-sh',
      'ps1': 'application/x-powershell',
    };

    return mimeTypes[ext] ?? 'application/octet-stream';
  }

  /// رفع مرفق
  /// تم تحسين عملية رفع الملفات بإضافة:
  /// - شريط تقدم للرفع مع عرض نسبة التقدم والوقت المتبقي
  /// - تنفيذ الرفع في الخلفية مع إمكانية متابعة العمل في التطبيق
  /// - إضافة إشعارات لإعلام المستخدم بانتهاء عملية الرفع
  Future<void> _uploadAttachment(String filePath, String fileType, String fileName, int fileSize) async {
    final task = _taskController.currentTask;
    if (task == null) return;
    
    final authUser = _authController.currentUser.value;
    if (authUser == null) {
      Get.snackbar('خطأ', 'يجب تسجيل الدخول لرفع المرفقات.');
      return;
    }

    // عرض مربع حوار تقدم الرفع
    final RxDouble uploadProgress = 0.0.obs;
    final RxString remainingTime = ''.obs;
    final RxBool isUploading = true.obs;

    // حساب الوقت المتبقي
    final startTime = DateTime.now();

    if (kIsWeb) {
      // في الويب، نحتاج لمعالجة مختلفة للملفات
      Get.snackbar(
        'غير مدعوم',
        'رفع الملفات غير مدعوم في نسخة الويب حالياً',
        backgroundColor: AppColors.statusPending,
        colorText: AppColors.white,
      );
      return;
    }

    // ignore: unnecessary_cast
    final File fileToUpload = File(filePath); // فقط في غير الويب

    // عرض مربع حوار التقدم المحسن
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Text('جاري رفع الملف'.tr, style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w700,
            )),
          ],
        ),
        content: Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الملف
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(FileProcessor.getFileIcon(filePath), size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(fileName,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        Text(FileProcessor.formatFileSize(fileSize),
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: uploadProgress.value,
              backgroundColor: AppColors.progressBackground,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 8),

            // نسبة التقدم والوقت المتبقي
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${(uploadProgress.value * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.w800,
                    color: AppColors.primary,
                  ),
                ),
                Text(remainingTime.value,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        )),
        actions: [
          TextButton(
            onPressed: () {
              isUploading.value = false;
              Get.back();
            },
            child: Text('إلغاء'.tr, style: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            )),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    try {
      // استدعاء دالة الرفع في TaskController
      final newAttachment = await _taskController.addAttachment(
        task.id,
        authUser.id,
        fileToUpload, // استخدام File object
        description: "مرفق جديد: $fileName",
        onUploadProgress: (progress) {
          if (!isUploading.value) return; // تحقق من الإلغاء
          uploadProgress.value = progress;
          _updateRemainingTime(startTime, progress, remainingTime);
        },
      );

      // إغلاق مربع الحوار بعد اكتمال الرفع أو الفشل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (newAttachment != null) {
        debugPrint('✅ تم رفع المرفق بنجاح: ${newAttachment.fileName}');

        // تحديث فوري للواجهة مع تحديث إضافي للتأكد
        if (mounted) {
          setState(() {});
          // تحديث إضافي بعد تأخير قصير
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              setState(() {});
            }
          });
        }

        // تحديث TaskController أيضاً مع إجبار التحديث
        _taskController.update(['task_details', 'task_attachments', 'task_overview']);
        _taskController.update(); // تحديث عام إضافي

        Get.snackbar(
          'نجاح'.tr,
          'تم رفع الملف "${newAttachment.fileName}" بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        debugPrint('Attachment added============================================================: [32m${newAttachment.fileName}[0m'); // Debugging line
      }
      else {
        Get.snackbar(
          'خطأ'.tr,
          _taskController.error.isNotEmpty ? 'فشل رفع الملف: ${_taskController.error}' : 'فشل رفع الملف.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مربع الحوار في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء رفع الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تحديث الوقت المتبقي للرفع
  void _updateRemainingTime(DateTime startTime, double progress, RxString remainingTime) {
    if (progress <= 0) {
      remainingTime.value = 'جاري الحساب...'.tr;
      return;
    }

    final elapsedSeconds = DateTime.now().difference(startTime).inSeconds;
    if (elapsedSeconds <= 0 || progress >= 1.0) {
      remainingTime.value = '';
      return;
    }

    // حساب الوقت المتبقي بناءً على التقدم الحالي والوقت المنقضي
    final totalSeconds = (elapsedSeconds / progress).round();
    final remainingSeconds = totalSeconds - elapsedSeconds;

    if (remainingSeconds < 60) {
      remainingTime.value = '$remainingSeconds ثانية'.tr;
    } else if (remainingSeconds < 3600) {
      final minutes = (remainingSeconds / 60).round();
      remainingTime.value = '$minutes دقيقة'.tr;
    } else {
      final hours = (remainingSeconds / 3600).round();
      remainingTime.value = '$hours ساعة'.tr;
    }
  }

  /// عرض المرفق
  /// تم تحسين عرض المرفقات باستخدام عارض الملفات الموحد
  /// الذي يوفر:
  /// - دعم معاينة ملفات PDF والمستندات
  /// - تحسين واجهة عرض الصور مع إمكانية التكبير والتصغير
  /// - إضافة خيارات تحرير للصور
  /// - واجهة موحدة لجميع أنواع الملفات
  void _viewAttachment(Attachment attachment) {
    // 🔒 التحقق من صلاحية عرض المرفقات
    if (!_permissionService.canViewAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لعرض المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // على الويب، نفترض أن الملف موجود دائمًا
    // على المنصات الأخرى، نتحقق من وجود الملف
    bool fileExists = true;
    if (!kIsWeb) {
      final file = File(attachment.filePath);
      fileExists = file.existsSync();
    }

    // التحقق من وجود الملف
    if (!fileExists) {
      Get.snackbar(
        'خطأ'.tr,
        'الملف غير موجود'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // استخدام عارض الملفات الموحد
    Get.to(() => FileViewerWidget(
      attachment: attachment,
      showToolbar: true,
      onDownload: _permissionService.canDownloadAttachments() ? _downloadAttachment : null,
      onShare: _permissionService.canShareAttachments() ? (attachment) {
        // يمكن تنفيذ وظيفة المشاركة هنا
        Get.snackbar(
          'مشاركة'.tr,
          'جاري مشاركة الملف: ${attachment.fileName}'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      } : null,
    ));
  }

  /// فتح الملف باستخدام التطبيق الافتراضي
  Future<void> _openAttachment(Attachment attachment) async {
    try {
      // على الويب، نفترض أن الملف موجود دائمًا
      // على المنصات الأخرى، نتحقق من وجود الملف
      bool fileExists = true;
      if (!kIsWeb) {
        final file = File(attachment.filePath);
        fileExists = file.existsSync();
      }

      // التحقق من وجود الملف
      if (!fileExists) {
        Get.snackbar(
          'خطأ'.tr,
          'الملف غير موجود'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
        return;
      }

      // استخدام خدمة التنزيل المحسنة
      final downloadService = DownloadService();

      // فتح الملف باستخدام التطبيق الافتراضي
      final result = await downloadService.openFile(attachment.filePath);

      if (!result['success']) {
        Get.snackbar(
          'خطأ'.tr,
          result['message'] ?? 'لا يمكن فتح الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء فتح الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }














  /// تنزيل المرفق
  /// تم تحسين وظيفة التنزيل لتوفير:
  /// - عرض تقدم التنزيل
  /// - إمكانية التنزيل في الخلفية
  /// - دعم استئناف التنزيل في حالة انقطاع الاتصال
  /// - إمكانية فتح الملف بعد التنزيل
  Future<void> _downloadAttachment(Attachment attachment, {bool openAfterDownload = false}) async {
    // 🔒 التحقق من صلاحية تنزيل المرفقات
    if (!_permissionService.canDownloadAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لتنزيل المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // متغيرات لتتبع حالة التنزيل
    final RxDouble progress = 0.0.obs;

    // عرض مربع حوار تقدم التنزيل
    Get.dialog(
      AlertDialog(
        title: Text('جاري التنزيل...'.tr),
        content: Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الملف
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(FileProcessor.getFileIcon(attachment.filePath)),
              title: Text(attachment.fileName, maxLines: 1, overflow: TextOverflow.ellipsis),
              subtitle: Text(FileProcessor.formatFileSize(attachment.fileSize)),
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: progress.value > 0 ? progress.value : null,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 8),

            // نسبة التقدم
            Text('${(progress.value * 100).toStringAsFixed(1)}%'),
          ],
        )),
        actions: [
          // زر إلغاء التنزيل
          TextButton(
            onPressed: () {
              // يمكن تنفيذ إلغاء التنزيل هنا
              Get.back();
            },
            child: Text('إلغاء'.tr),
          ),
          // زر تنفيذ التنزيل في الخلفية
          TextButton(
            onPressed: () {
              Get.back();
              // استمرار التنزيل في الخلفية
              Get.snackbar(
                'تنزيل في الخلفية'.tr,
                'يتم تنزيل الملف في الخلفية'.tr,
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: Text('تنفيذ في الخلفية'.tr),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    try {
      // تنزيل حقيقي للملف
      final downloadService = DownloadService();
      final success = await downloadService.downloadAttachment(
        attachment,
        onProgress: (received, total) {
          if (total > 0) {
            progress.value = received / total;
          }
        },
        openAfterDownload: openAfterDownload,
      );

      // إغلاق مربع الحوار
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (success) {
        // عرض رسالة نجاح
        Get.snackbar(
          'نجاح'.tr,
          'تم تنزيل الملف "${attachment.fileName}" بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 3),
        );
      } else {
        // عرض رسالة فشل
        Get.snackbar(
          'خطأ'.tr,
          'فشل في تنزيل الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مربع الحوار في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تنزيل الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// حذف المرفق
  void _deleteAttachment(Attachment attachment) {
    // 🔒 التحقق من صلاحية حذف المرفقات
    if (!_permissionService.canDeleteAttachments()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لحذف المرفقات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: Text('تأكيد الحذف'.tr),
        content: Text('هل أنت متأكد من حذف هذا المرفق؟'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();

              // عرض مؤشر التحميل
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );

              try {
                // حذف المرفق
                final success = await _attachmentsController.deleteAttachment(attachment.id);

                // إغلاق مؤشر التحميل
                Get.back();

                if (success) {
                  // جلب بيانات المهمة من السيرفر بعد الحذف
                  await _taskController.refreshTaskDetails(_taskController.currentTask!.id);
                  // تحديث فوري للواجهة
                  _taskController.update(['task_details']);

                  Get.snackbar(
                    'نجاح'.tr,
                    'تم حذف المرفق بنجاح'.tr,
                  );
                } else {
                  Get.snackbar(
                    'خطأ'.tr,
                    'فشل حذف المرفق'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              } catch (e) {
                // إغلاق مؤشر التحميل
                Get.back();

                Get.snackbar(
                  'خطأ'.tr,
                  'حدث خطأ أثناء حذف المرفق: e.toString()}'.tr,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'.tr),
          ),
        ],
      ),
    );
  }

  /// التحقق من إمكانية حذف المرفق
  bool _canDeleteAttachment(Attachment attachment) {
    // 🔒 التحقق من صلاحية حذف المرفقات أولاً
    if (!_permissionService.canDeleteAttachments()) {
      return false;
    }

    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return false;

    // إذا كان لديه صلاحية إدارة المرفقات، يمكنه حذف أي مرفق
    if (_permissionService.canManageAttachments()) {
      return true;
    }

    // المستخدم الذي رفع المرفق يمكنه حذفه (إذا كان لديه صلاحية الحذف)
    if (attachment.uploadedBy == currentUser.id) return true;

    // المدير يمكنه حذف أي مرفق (إذا كان لديه صلاحية الحذف)
    if (currentUser.role == UserRole.admin) return true;

    // مدير القسم يمكنه حذف مرفقات قسمه (إذا كان لديه صلاحية الحذف)
    final task = _taskController.currentTask;
    if (task != null && currentUser.role == UserRole.manager && task.departmentId == currentUser.departmentId) {
      return true;
    }

    return false;
  }

  String _getMimeCategory(String? mimeType, [String? fileName]) {
    if (mimeType == null && fileName == null) return 'other';

    // إذا كان نوع MIME صحيح، استخدمه
    if (mimeType != null && mimeType != 'application/octet-stream') {
      if (mimeType.startsWith('image/')) return 'image';
      if (mimeType.startsWith('video/')) return 'video';
      if (mimeType.startsWith('audio/')) return 'audio';
      if (mimeType == 'application/pdf' ||
          mimeType.contains('document') || // Covers .doc, .docx, .odt etc.
          mimeType.contains('msword') || mimeType.contains('officedocument.wordprocessingml') ||
          mimeType.contains('excel') || mimeType.contains('spreadsheetml') ||
          mimeType.contains('powerpoint') || mimeType.contains('presentationml')) {
        return 'document';
      }
      if (mimeType.contains('zip') || mimeType.contains('rar') || mimeType.contains('compressed')) {
        return 'archive';
      }
      if (mimeType.contains('executable') || mimeType.contains('android.package') ||
          mimeType.contains('x-msi') || mimeType.contains('x-bat')) {
        return 'executable';
      }
    }

    // إذا كان نوع MIME غير صحيح أو application/octet-stream، استخدم امتداد الملف
    if (fileName != null) {
      final extension = fileName.split('.').last.toLowerCase();
      switch (extension) {
        // صور
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
        case 'svg':
        case 'tiff':
        case 'ico':
          return 'image';

        // فيديو
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'flv':
        case 'webm':
        case 'mkv':
        case '3gp':
        case 'm4v':
          return 'video';

        // صوت
        case 'mp3':
        case 'wav':
        case 'ogg':
        case 'aac':
        case 'flac':
        case 'm4a':
        case 'wma':
          return 'audio';

        // مستندات
        case 'pdf':
        case 'doc':
        case 'docx':
        case 'xls':
        case 'xlsx':
        case 'ppt':
        case 'pptx':
        case 'txt':
        case 'rtf':
        case 'csv':
        case 'odt':
        case 'ods':
        case 'odp':
          return 'document';

        // أرشيف
        case 'zip':
        case 'rar':
        case '7z':
        case 'tar':
        case 'gz':
        case 'bz2':
        case 'xz':
          return 'archive';

        // ملفات تنفيذية
        case 'exe':
        case 'msi':
        case 'apk':
        case 'deb':
        case 'rpm':
        case 'dmg':
        case 'app':
        case 'jar':
        case 'war':
        case 'ear':
          return 'executable';

        // ملفات برمجية
        case 'js':
        case 'css':
        case 'html':
        case 'htm':
        case 'php':
        case 'py':
        case 'java':
        case 'cpp':
        case 'c':
        case 'h':
        case 'sql':
        case 'json':
        case 'xml':
        case 'yaml':
        case 'yml':
        case 'sh':
        case 'bat':
        case 'cmd':
        case 'ps1':
          return 'code';

        default:
          return 'other';
      }
    }

    return 'other';
  }

  /// الحصول على أيقونة الملف الشاملة
  static IconData getComprehensiveFileIcon(String fileName, [String? mimeType]) {
    final extension = fileName.split('.').last.toLowerCase();

    // أيقونات حسب الامتداد
    switch (extension) {
      // مستندات PDF
      case 'pdf':
        return Icons.picture_as_pdf;

      // مستندات Word
      case 'doc':
      case 'docx':
      case 'odt':
        return Icons.description;

      // جداول بيانات
      case 'xls':
      case 'xlsx':
      case 'ods':
      case 'csv':
        return Icons.table_chart;

      // عروض تقديمية
      case 'ppt':
      case 'pptx':
      case 'odp':
        return Icons.slideshow;

      // ملفات نصية
      case 'txt':
      case 'rtf':
        return Icons.text_snippet;

      // صور
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
      case 'tiff':
      case 'ico':
        return Icons.image;

      // فيديو
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
      case 'mkv':
      case '3gp':
      case 'm4v':
        return Icons.video_file;

      // صوت
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'aac':
      case 'flac':
      case 'm4a':
      case 'wma':
        return Icons.audio_file;

      // أرشيف
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
      case 'bz2':
      case 'xz':
        return Icons.folder_zip;

      // ملفات تنفيذية - أيقونات خاصة مع تحذير
      case 'exe':
      case 'msi':
        return Icons.launch; // أيقونة تشغيل للملفات التنفيذية

      case 'apk':
        return Icons.android; // أيقونة Android للـ APK

      case 'deb':
      case 'rpm':
      case 'snap':
      case 'appimage':
        return Icons.computer; // أيقونة كمبيوتر لحزم Linux

      case 'dmg':
      case 'app':
        return Icons.laptop_mac; // أيقونة Mac

      case 'jar':
      case 'war':
      case 'ear':
        return Icons.coffee; // أيقونة قهوة لملفات Java

      // ملفات برمجية
      case 'js':
        return Icons.javascript;

      case 'html':
      case 'htm':
        return Icons.web;

      case 'css':
        return Icons.style;

      case 'php':
      case 'py':
      case 'java':
      case 'cpp':
      case 'c':
      case 'h':
        return Icons.code;

      case 'sql':
        return Icons.storage;

      case 'json':
      case 'xml':
      case 'yaml':
      case 'yml':
        return Icons.data_object;

      case 'sh':
      case 'bash':
      case 'zsh':
      case 'fish':
        return Icons.terminal;

      case 'bat':
      case 'cmd':
        return Icons.computer;

      case 'ps1':
      case 'psm1':
        return Icons.power_settings_new;

      // ملفات خاصة
      case 'iso':
        return Icons.album;

      case 'torrent':
        return Icons.cloud_download;

      case 'key':
      case 'pem':
      case 'crt':
      case 'cer':
        return Icons.security;

      case 'log':
        return Icons.list_alt;

      case 'cfg':
      case 'conf':
      case 'ini':
        return Icons.settings;

      case 'db':
      case 'sqlite':
      case 'mdb':
        return Icons.storage;

      case 'font':
      case 'ttf':
      case 'otf':
      case 'woff':
      case 'woff2':
        return Icons.font_download;

      default:
        // استخدام نوع MIME كبديل
        if (mimeType != null) {
          if (mimeType.startsWith('image/')) return Icons.image;
          if (mimeType.startsWith('video/')) return Icons.video_file;
          if (mimeType.startsWith('audio/')) return Icons.audio_file;
          if (mimeType.startsWith('text/')) return Icons.text_snippet;
          if (mimeType.contains('pdf')) return Icons.picture_as_pdf;
          if (mimeType.contains('zip') || mimeType.contains('compressed')) return Icons.folder_zip;
          if (mimeType.contains('executable')) return Icons.launch;
        }

        return Icons.insert_drive_file; // أيقونة افتراضية
    }
  }

  /// الحصول على لون الأيقونة حسب نوع الملف
  static Color getFileIconColor(String fileName, [String? mimeType]) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      // مستندات - أزرق
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'xls':
      case 'xlsx':
        return Colors.green;
      case 'ppt':
      case 'pptx':
        return Colors.orange;

      // صور - أخضر
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
        return Colors.green;

      // فيديو - بنفسجي
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return Colors.purple;

      // صوت - برتقالي
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'aac':
        return Colors.orange;

      // أرشيف - بني
      case 'zip':
      case 'rar':
      case '7z':
        return Colors.brown;

      // ملفات تنفيذية - أحمر (تحذير)
      case 'exe':
      case 'msi':
      case 'apk':
        return Colors.red.shade700;

      // ملفات برمجية - أزرق داكن
      case 'js':
      case 'html':
      case 'css':
      case 'php':
      case 'py':
      case 'java':
        return Colors.indigo;

      default:
        return Colors.grey.shade600;
    }
  }
}
