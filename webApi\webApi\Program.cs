﻿﻿﻿﻿﻿﻿﻿using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using webApi.Models;
using webApi.Services;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Security.Claims;

// إعداد الترميز للنصوص العربية
Console.OutputEncoding = Encoding.UTF8;
Console.InputEncoding = Encoding.UTF8;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// إضافة خدمات CORS
var corsSettings = builder.Configuration.GetSection("Cors");
builder.Services.AddCors(options =>
{
    // سياسة مفتوحة للتطوير
    options.AddPolicy("AllowFlutterApp", policy =>
    {
        policy
            .AllowAnyOrigin() // السماح لجميع المصادر (للتطوير فقط)
            .AllowAnyMethod() // السماح لجميع الطرق
            .AllowAnyHeader(); // السماح لجميع الرؤوس
    });

    // سياسة مقيدة للإنتاج باستخدام إعدادات التكوين
    options.AddPolicy("ProductionPolicy", policy =>
    {
        var allowedOrigins = corsSettings.GetSection("AllowedOrigins").Get<string[]>() ??
            new[] { "http://localhost:8080", "https://localhost:8080" };
        var allowedMethods = corsSettings.GetSection("AllowedMethods").Get<string[]>() ??
            new[] { "GET", "POST", "PUT", "DELETE", "OPTIONS" };
        var allowedHeaders = corsSettings.GetSection("AllowedHeaders").Get<string[]>() ??
            new[] { "Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin" };
        var allowCredentials = corsSettings.GetValue<bool>("AllowCredentials", true);
        var preflightMaxAge = corsSettings.GetValue<int>("PreflightMaxAge", 86400);

        policy
            .WithOrigins(allowedOrigins)
            .WithMethods(allowedMethods)
            .WithHeaders(allowedHeaders)
            .SetPreflightMaxAge(TimeSpan.FromSeconds(preflightMaxAge));

        if (allowCredentials)
        {
            policy.AllowCredentials();
        }
    });

    // سياسة مخصصة للـ Flutter Web
    options.AddPolicy("FlutterWebPolicy", policy =>
    {
        policy
            .WithOrigins(
                "http://localhost:8080",
                "https://localhost:8080",
                "http://127.0.0.1:8080",
                "https://127.0.0.1:8080"
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .SetPreflightMaxAge(TimeSpan.FromHours(24));
    });
});

// Configure Entity Framework
builder.Services.AddDbContext<TasksDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// إضافة خدمات المصادقة والتفويض المحسنة
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<UserPermissionService>(); // خدمة الصلاحيات المحسنة
builder.Services.AddScoped<DatabaseSeeder>();

// إضافة خدمة التخزين المؤقت للأداء
builder.Services.AddMemoryCache();

// إضافة خدمات نظام الأرشفة والمستندات
builder.Services.AddScoped<ArchiveDocumentService>();
builder.Services.AddScoped<TaskDocumentService>();

// إضافة خدمة الإشعارات
builder.Services.AddScoped<INotificationService, NotificationService>();

// إضافة خدمة التسجيل الموحدة
builder.Services.AddScoped<ILoggingService, LoggingService>();

// إضافة خدمة تذكيرات المهام كـ Background Service
builder.Services.AddHostedService<TaskReminderService>();

// إضافة خدمة تذكيرات التقويم كـ Background Service
builder.Services.AddHostedService<CalendarReminderService>();

// إضافة خدمة التخزين المحلي البسيطة والموثوقة للملفات
builder.Services.AddScoped<SimpleLocalFileStorageService>();

// Add SignalR services with enhanced configuration
builder.Services.AddSignalR(options =>
{
    // تكوين خيارات SignalR
    options.EnableDetailedErrors = true; // تمكين رسائل الخطأ المفصلة (للتطوير فقط)
    options.MaximumReceiveMessageSize = 102400; // 100 KB
    options.StreamBufferCapacity = 20; // عدد الرسائل المخزنة مؤقتًا
    options.HandshakeTimeout = TimeSpan.FromSeconds(15); // مهلة المصافحة
    options.KeepAliveInterval = TimeSpan.FromSeconds(15); // فاصل الحفاظ على الاتصال
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30); // مهلة العميل
});

// تكوين JWT Authentication
var jwtSettings = builder.Configuration.GetSection("Jwt");
var secretKey = jwtSettings["SecretKey"];
var issuer = jwtSettings["Issuer"];
var audience = jwtSettings["Audience"];

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = issuer,
        ValidAudience = audience,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey!)),
        ClockSkew = TimeSpan.Zero
    };

    // إضافة معالجة أحداث JWT
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            Console.WriteLine($"Authentication failed: {context.Exception.Message}");
            return System.Threading.Tasks.Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            // الحصول على اسم المستخدم من Claims بشكل صحيح
            var userName = context.Principal?.FindFirst(ClaimTypes.Name)?.Value ??
                          context.Principal?.FindFirst("Username")?.Value ??
                          context.Principal?.FindFirst("UserId")?.Value ??
                          "مستخدم غير معروف";
            Console.WriteLine($"Token validated for user: {userName}");
            return System.Threading.Tasks.Task.CompletedTask;
        }
    };
});

// إضافة Authorization
builder.Services.AddAuthorization();

// تكوين JSON Serialization لحل مشكلة المراجع الدائرية ودعم الترميز العربي
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        // إعداد الترميز لدعم النصوص العربية
        options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
        options.JsonSerializerOptions.NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
        options.JsonSerializerOptions.MaxDepth = 32;
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
        // إضافة دعم الترميز العربي
        options.JsonSerializerOptions.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
    });

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

builder.Services.AddEndpointsApiExplorer();

// Configure Swagger/OpenAPI
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Tasks Management API",
        Version = "v1",
        Description = "A comprehensive task management system API with user management, messaging, dashboards, and more.",
        Contact = new OpenApiContact
        {
            Name = "Tasks Management System",
            Email = "<EMAIL>"
        }
    });

    // إضافة دعم JWT في Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Enable XML comments if you have them
    // var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    // var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    // c.IncludeXmlComments(xmlPath);
});

var app = builder.Build();

// Configure the HTTP request pipeline.

// تكوين الترميز العربي للتطبيق
System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

// Enable Swagger in all environments for better API documentation
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Tasks Management API v1");
    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
    c.DocumentTitle = "Tasks Management API Documentation";
    c.DefaultModelsExpandDepth(-1); // Hide models section by default
    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None); // Collapse all operations by default
});

// Also enable OpenAPI
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// تعطيل إعادة التوجيه للـ HTTPS في بيئة التطوير لتجنب مشاكل Flutter
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// إضافة CORS middleware (يجب أن يكون قبل المصادقة والتفويض)
// استخدام السياسة المفتوحة للسماح بالاتصال من أي مكان
app.UseCors("AllowFlutterApp"); // سياسة مفتوحة تسمح بالاتصال من أي مكان
Console.WriteLine("🌐 CORS enabled for all origins - يمكن الاتصال من أي مكان");

// إعداد الملفات الثابتة لدعم رفع الملفات
app.UseStaticFiles();

// إضافة middleware للمصادقة والتفويض
// إضافة middleware لتسجيل جميع الطلبات
app.Use(async (context, next) =>
{
    Console.WriteLine($"🌐 {context.Request.Method} {context.Request.Path} - من {context.Request.Headers["User-Agent"]}");
    if (context.Request.Method == "POST")
    {
        Console.WriteLine($"📋 POST Request Body Length: {context.Request.ContentLength}");
        Console.WriteLine($"📋 Content-Type: {context.Request.ContentType}");

        // قراءة محتوى الطلب للتشخيص
        if (context.Request.Path.StartsWithSegments("/api/UserPermissions") && !context.Request.Path.Value.Contains("check"))
        {
            context.Request.EnableBuffering();
            var body = await new StreamReader(context.Request.Body).ReadToEndAsync();
            context.Request.Body.Position = 0;
            Console.WriteLine($"📋 POST Body Content: {body}");

            // تشخيص إضافي للـ routing
            Console.WriteLine($"🔍 Request Path: {context.Request.Path}");
            Console.WriteLine($"🔍 Request Method: {context.Request.Method}");
            Console.WriteLine($"🔍 Content-Type: {context.Request.ContentType}");
        }
    }
    await next();
    Console.WriteLine($"📤 Response: {context.Response.StatusCode}");

    // تشخيص إضافي للاستجابة
    if (context.Request.Path.StartsWithSegments("/api/UserPermissions") && context.Request.Method == "POST" && context.Response.StatusCode == 400)
    {
        Console.WriteLine($"⚠️ POST UserPermissions فشل بـ 400 - تحقق من Model Binding");
    }
});

app.UseAuthentication();
app.UseAuthorization();

// إضافة Logging Middleware لتسجيل جميع العمليات
app.UseMiddleware<webApi.Middleware.LoggingMiddleware>();

app.MapControllers();

// Map SignalR Hubs
app.MapHub<webApi.Hubs.TaskHub>("/taskHub");
app.MapHub<webApi.Hubs.ChatHub>("/chatHub");
app.MapHub<webApi.Hubs.TaskCommentsHub>("/taskCommentsHub");
app.MapHub<webApi.Hubs.NotificationHub>("/notificationHub");

// إعداد الخادم للاستماع على جميع واجهات الشبكة
Console.WriteLine("🚀 Starting server on all network interfaces...");
Console.WriteLine("🌐 Server accessible from any IP address");
Console.WriteLine("📡 HTTP: http://0.0.0.0:7111");
Console.WriteLine("🔒 HTTPS: https://0.0.0.0:7112");

app.Run();
