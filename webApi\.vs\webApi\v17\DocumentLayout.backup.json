{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\appsettings.json", "RelativeDocumentMoniker": "webApi\\appsettings.json", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\appsettings.json", "RelativeToolTip": "webApi\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-22T17:50:30.299Z", "EditorCaption": ""}]}]}]}