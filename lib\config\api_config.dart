import 'package:get/get.dart';
import '../services/dynamic_server_config_service.dart';

/// إعدادات API للتطبيق
/// متوافق مع ASP.NET Core API - محدث لدعم IP الديناميكي
class ApiConfig {
  /// عناوين الخادم الافتراضية (fallback)
  static const String _defaultApiUrl = 'http://localhost:7111';
  static const String _defaultApiUrlHttps = 'https://localhost:7112';

  /// عنوان خادم WebSocket (Node.js) - ثابت حالياً
  static String websocketUrl = 'ws://localhost:8080/ws';

  /// مفتاح API للخرائط
  static String mapsApiKey = '';

  /// حجم الصفحة الافتراضي
  static int defaultPageSize = 20;

  /// الحد الأقصى لحجم الملف (بالبايت)
  static int maxFileSize = 200 * 1024 * 1024; // 200 ميجابايت

  /// الوقت المستقطع للطلبات (بالثواني)
  static int requestTimeout = 30;

  /// ما إذا كان التطبيق في وضع التطوير
  static bool isDevelopmentMode = true;

  /// عنوان URL الأساسي للخادم - ديناميكي أو افتراضي
  static String get baseUrl {
    try {
      if (Get.isRegistered<DynamicServerConfigService>()) {
        final serverConfig = Get.find<DynamicServerConfigService>();
        return serverConfig.baseUrl;
      }
    } catch (e) {
      // في حالة عدم توفر الخدمة، استخدم القيمة الافتراضية
    }
    return _defaultApiUrl;
  }

  /// الحصول على عنوان API الديناميكي أو الافتراضي
  static String get apiUrl {
    return baseUrl;
  }

  /// الحصول على عنوان API HTTPS الديناميكي أو الافتراضي
  static String get apiUrlHttps {
    try {
      if (Get.isRegistered<DynamicServerConfigService>()) {
        final serverConfig = Get.find<DynamicServerConfigService>();
        if (serverConfig.useHttps) {
          return serverConfig.baseUrl;
        }
      }
    } catch (e) {
      // في حالة عدم توفر الخدمة، استخدم القيمة الافتراضية
    }
    return _defaultApiUrlHttps;
  }

  /// الحصول على عنوان API المناسب (HTTP أو HTTPS)
  static String getApiUrl({bool useHttps = false}) {
    return useHttps ? apiUrlHttps : apiUrl;
  }

  /// الحصول على العنوان الافتراضي (للـ fallback)
  static String getDefaultApiUrl({bool useHttps = false}) {
    return useHttps ? _defaultApiUrlHttps : _defaultApiUrl;
  }

  /// تهيئة التكوين - محدث للعمل مع النظام الديناميكي
  static void initialize({
    String? serverIp,
    int? serverPort,
    bool? useHttps,
    String? websocketUrl,
    String? mapsApiKey,
    int? defaultPageSize,
    int? maxFileSize,
    int? requestTimeout,
    bool? isDevelopmentMode,
  }) async {
    // تحديث إعدادات الخادم الديناميكي إذا كانت متوفرة
    if (serverIp != null || serverPort != null || useHttps != null) {
      try {
        if (Get.isRegistered<DynamicServerConfigService>()) {
          final serverConfig = Get.find<DynamicServerConfigService>();
          await serverConfig.updateServerConfig(
            ip: serverIp ?? serverConfig.serverIp,
            port: serverPort ?? serverConfig.serverPort,
            useHttps: useHttps ?? serverConfig.useHttps,
            testConnection: false, // لا نختبر الاتصال أثناء التهيئة
          );
        }
      } catch (e) {
        // تجاهل الأخطاء أثناء التهيئة
      }
    }

    // تحديث الإعدادات الأخرى
    if (websocketUrl != null) ApiConfig.websocketUrl = websocketUrl;
    if (mapsApiKey != null) ApiConfig.mapsApiKey = mapsApiKey;
    if (defaultPageSize != null) ApiConfig.defaultPageSize = defaultPageSize;
    if (maxFileSize != null) ApiConfig.maxFileSize = maxFileSize;
    if (requestTimeout != null) ApiConfig.requestTimeout = requestTimeout;
    if (isDevelopmentMode != null) ApiConfig.isDevelopmentMode = isDevelopmentMode;
  }

  /// تهيئة خدمة الخادم الديناميكي
  static Future<void> initializeDynamicServerConfig() async {
    try {
      if (!Get.isRegistered<DynamicServerConfigService>()) {
        Get.put(DynamicServerConfigService(), permanent: true);
      }
    } catch (e) {
      // تجاهل الأخطاء - سيتم استخدام القيم الافتراضية
    }
  }

  /// مهلة الاتصال بالثواني
  static const int connectionTimeout = 30;
  
  /// مهلة الاستقبال بالثواني
  static const int receiveTimeout = 30;
  
  /// إصدار API
  static const String apiVersion = 'v1';
  
  /// رؤوس HTTP الافتراضية
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json; charset=UTF-8',
    'Accept': 'application/json',
  };
  
  /// نقاط النهاية للـ API
  static const String authEndpoint = '/api/auth';
  static const String usersEndpoint = '/api/users';
  static const String tasksEndpoint = '/api/tasks';
  static const String departmentsEndpoint = '/api/departments';
  static const String reportsEndpoint = '/api/reports';
  static const String contributionReportsEndpoint = '/api/contribution-reports';
  static const String attachmentsEndpoint = '/api/attachments';
  static const String commentsEndpoint = '/api/comments';
  static const String notificationsEndpoint = '/api/notifications';
  static const String calendarEventsEndpoint = '/api/calendar-events';
  static const String chatGroupsEndpoint = '/api/chat-groups';
  static const String messagesEndpoint = '/api/messages';
  static const String archiveDocumentsEndpoint = '/api/archive-documents';
  static const String archiveCategoriesEndpoint = '/api/archive-categories';
  static const String taskDocumentsEndpoint = '/api/TaskDocuments';
  static const String systemLogsEndpoint = '/api/system-logs';
  static const String activityLogsEndpoint = '/api/activity-logs';
  static const String backupsEndpoint = '/api/backups';
  static const String systemSettingsEndpoint = '/api/system-settings';
  static const String userPermissionsEndpoint = '/api/user-permissions';
  static const String rolePermissionsEndpoint = '/api/role-permissions';
  static const String reportSchedulesEndpoint = '/api/report-schedules';
  static const String taskTypesEndpoint = '/api/task-types';
  static const String taskStatusEndpoint = '/api/task-status';
  static const String taskPriorityEndpoint = '/api/task-priority';
  static const String timeTrackingEndpoint = '/api/time-tracking';

  // نقاط النهاية لإدارة النظام
  static const String systemRestartEndpoint = '/api/SystemSettings/restart';
  static const String systemRepairEndpoint = '/api/SystemSettings/repair-database';
  static const String systemInfoEndpoint = '/api/SystemSettings/system-info';
  
  /// إعدادات البيئة
  static const bool isProduction = false;
  static const bool enableLogging = true;
  static const bool enableDebugMode = true;
  
  /// إعدادات التخزين المؤقت
  static const int cacheMaxAge = 300; // 5 دقائق
  static const int maxCacheSize = 50 * 1024 * 1024; // 50 MB
  
  /// إعدادات الملفات - تم دمجها من أعلى
  static const List<String> allowedFileTypes = [
    // صور
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
    // مستندات
    'pdf', 'doc', 'docx', 'txt', 'rtf', 'odt',
    // فيديو
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm',
    // صوت
    'mp3', 'wav', 'flac', 'aac', 'ogg',
    // ملفات بيانات
    'csv', 'json', 'xml', 'xlsx', 'xls',
    // ملفات مضغوطة
    'zip', 'rar', '7z'
  ];
  
  /// إعدادات الصفحات - تم دمجها من أعلى
  static const int maxPageSize = 100;
  
  /// إعدادات إعادة المحاولة
  static const int maxRetryAttempts = 3;
  static const int retryDelay = 1000; // milliseconds
  
  /// الحصول على URL كامل لنقطة نهاية معينة
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  /// الحصول على رؤوس HTTP مع التوكن
  static Map<String, String> getHeadersWithToken(String? token) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
  
  /// التحقق من صحة نوع الملف
  static bool isFileTypeAllowed(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return allowedFileTypes.contains(extension);
  }
  
  /// التحقق من حجم الملف
  static bool isFileSizeAllowed(int fileSize) {
    return fileSize <= maxFileSize;
  }
  
  /// الحصول على معرف فريد للطلب
  static String generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
