{"profiles": {"http": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://0.0.0.0:7111"}, "https": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://0.0.0.0:7112;http://0.0.0.0:7111"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:54730/", "sslPort": 44303}}}